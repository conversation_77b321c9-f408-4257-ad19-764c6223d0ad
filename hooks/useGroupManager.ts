/**
 * useGroupManager Hook
 * 🎯 核心价值：在React组件中使用GroupManager的统一接口
 * ⚡ 性能优化：memoization、批量操作、状态缓存
 * 📊 功能范围：分组控制、可见性管理、批量操作
 * 🔄 架构设计：响应式分组管理、类型安全
 */

import { useState, useCallback, useMemo, useEffect } from 'react';
import type { CellData } from '../types/grid';
import { 
  GroupManager, 
  type GroupConfig, 
  type GroupOperationResult, 
  type GroupStats 
} from '../utils/groupManager';
import { type GroupType } from '../utils/CellDataManager';

// Hook返回类型
export interface UseGroupManagerReturn {
  // 分组配置
  groupConfigs: Map<number, GroupConfig>;
  getGroupConfig: (groupId: number) => GroupConfig | undefined;
  setGroupConfig: (groupId: number, config: Partial<GroupConfig>) => boolean;

  // 分组操作
  addCellsToGroup: (cellIds: string[], groupId: number) => GroupOperationResult;
  removeCellsFromGroup: (cellIds: string[]) => GroupOperationResult;
  clearGroup: (groupId: number) => GroupOperationResult;
  clearAllGroups: () => GroupOperationResult;

  // 可见性控制
  toggleGroupVisibility: (groupId: number) => boolean;
  setGroupVisibility: (groupId: number, isVisible: boolean) => void;
  showAllCrossGroups: () => void;
  hideAllCrossGroups: () => void;
  showAllDiagonalGroups: () => void;
  hideAllDiagonalGroups: () => void;
  showAllGroups: () => void;
  hideAllGroups: () => void;

  // 分组激活
  activateGroup: (groupId: number) => boolean;
  deactivateGroup: (groupId: number) => boolean;
  deactivateAllGroups: () => void;

  // 查询功能
  getGroupCells: (groupId: number) => CellData[];
  getGroupsByType: (type: GroupType) => GroupConfig[];
  getVisibleGroups: () => GroupConfig[];
  getActiveGroups: () => GroupConfig[];
  isGroupEmpty: (groupId: number) => boolean;

  // 颜色管理
  getGroupColor: (groupId: number) => string;
  setGroupColor: (groupId: number, color: string) => boolean;

  // 统计信息
  stats: GroupStats;
  refreshStats: () => void;

  // 工具函数
  getGroupType: (groupId: number) => GroupType | null;
  resetGroupConfigs: () => void;

  // 状态
  isLoading: boolean;
  error: string | null;
}

/**
 * GroupManager React Hook
 */
export function useGroupManager(): UseGroupManagerReturn {
  const [groupConfigs, setGroupConfigs] = useState<Map<number, GroupConfig>>(new Map());
  const [stats, setStats] = useState<GroupStats>({
    totalGroups: 0,
    activeGroups: 0,
    crossGroups: 0,
    diagonalGroups: 0,
    cellsInGroups: 0,
    groupDistribution: {},
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取GroupManager实例
  const manager = useMemo(() => GroupManager.getInstance(), []);

  // 刷新分组配置
  const refreshConfigs = useCallback(() => {
    try {
      setIsLoading(true);
      const configs = new Map<number, GroupConfig>();
      
      // 获取所有分组配置
      for (let i = 1; i <= 44; i++) {
        const config = manager.getGroupConfig(i);
        if (config) {
          configs.set(i, config);
        }
      }
      
      setGroupConfigs(configs);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to refresh configs');
    } finally {
      setIsLoading(false);
    }
  }, [manager]);

  // 刷新统计信息
  const refreshStats = useCallback(() => {
    try {
      const newStats = manager.getGroupStats();
      setStats(newStats);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to refresh stats');
    }
  }, [manager]);

  // 初始化
  useEffect(() => {
    refreshConfigs();
    refreshStats();
  }, [refreshConfigs, refreshStats]);

  // 分组配置函数
  const getGroupConfig = useCallback((groupId: number) => {
    return manager.getGroupConfig(groupId);
  }, [manager]);

  const setGroupConfig = useCallback((groupId: number, config: Partial<GroupConfig>): boolean => {
    try {
      const success = manager.setGroupConfig(groupId, config);
      if (success) {
        refreshConfigs();
        refreshStats();
      }
      return success;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to set group config');
      return false;
    }
  }, [manager, refreshConfigs, refreshStats]);

  // 分组操作函数
  const addCellsToGroup = useCallback((cellIds: string[], groupId: number): GroupOperationResult => {
    try {
      const result = manager.addCellsToGroup(cellIds, groupId);
      if (result.success) {
        refreshStats();
      }
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to add cells to group';
      setError(errorMessage);
      return {
        success: false,
        affectedCells: 0,
        message: errorMessage,
      };
    }
  }, [manager, refreshStats]);

  const removeCellsFromGroup = useCallback((cellIds: string[]): GroupOperationResult => {
    try {
      const result = manager.removeCellsFromGroup(cellIds);
      if (result.success) {
        refreshStats();
      }
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to remove cells from group';
      setError(errorMessage);
      return {
        success: false,
        affectedCells: 0,
        message: errorMessage,
      };
    }
  }, [manager, refreshStats]);

  const clearGroup = useCallback((groupId: number): GroupOperationResult => {
    try {
      const result = manager.clearGroup(groupId);
      if (result.success) {
        refreshStats();
      }
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to clear group';
      setError(errorMessage);
      return {
        success: false,
        affectedCells: 0,
        message: errorMessage,
      };
    }
  }, [manager, refreshStats]);

  const clearAllGroups = useCallback((): GroupOperationResult => {
    try {
      const result = manager.clearAllGroups();
      if (result.success) {
        refreshStats();
      }
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to clear all groups';
      setError(errorMessage);
      return {
        success: false,
        affectedCells: 0,
        message: errorMessage,
      };
    }
  }, [manager, refreshStats]);

  // 可见性控制函数
  const toggleGroupVisibility = useCallback((groupId: number): boolean => {
    try {
      const result = manager.toggleGroupVisibility(groupId);
      refreshConfigs();
      return result;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to toggle group visibility');
      return false;
    }
  }, [manager, refreshConfigs]);

  const setGroupVisibility = useCallback((groupId: number, isVisible: boolean): void => {
    try {
      manager.setGroupVisibility(groupId, isVisible);
      refreshConfigs();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to set group visibility');
    }
  }, [manager, refreshConfigs]);

  const showAllCrossGroups = useCallback((): void => {
    try {
      manager.showAllCrossGroups();
      refreshConfigs();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to show all cross groups');
    }
  }, [manager, refreshConfigs]);

  const hideAllCrossGroups = useCallback((): void => {
    try {
      manager.hideAllCrossGroups();
      refreshConfigs();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to hide all cross groups');
    }
  }, [manager, refreshConfigs]);

  const showAllDiagonalGroups = useCallback((): void => {
    try {
      manager.showAllDiagonalGroups();
      refreshConfigs();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to show all diagonal groups');
    }
  }, [manager, refreshConfigs]);

  const hideAllDiagonalGroups = useCallback((): void => {
    try {
      manager.hideAllDiagonalGroups();
      refreshConfigs();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to hide all diagonal groups');
    }
  }, [manager, refreshConfigs]);

  const showAllGroups = useCallback((): void => {
    try {
      manager.showAllGroups();
      refreshConfigs();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to show all groups');
    }
  }, [manager, refreshConfigs]);

  const hideAllGroups = useCallback((): void => {
    try {
      manager.hideAllGroups();
      refreshConfigs();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to hide all groups');
    }
  }, [manager, refreshConfigs]);

  // 分组激活函数
  const activateGroup = useCallback((groupId: number): boolean => {
    try {
      const result = manager.activateGroup(groupId);
      if (result) {
        refreshConfigs();
        refreshStats();
      }
      return result;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to activate group');
      return false;
    }
  }, [manager, refreshConfigs, refreshStats]);

  const deactivateGroup = useCallback((groupId: number): boolean => {
    try {
      const result = manager.deactivateGroup(groupId);
      if (result) {
        refreshConfigs();
        refreshStats();
      }
      return result;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to deactivate group');
      return false;
    }
  }, [manager, refreshConfigs, refreshStats]);

  const deactivateAllGroups = useCallback((): void => {
    try {
      manager.deactivateAllGroups();
      refreshConfigs();
      refreshStats();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to deactivate all groups');
    }
  }, [manager, refreshConfigs, refreshStats]);

  // 查询函数
  const getGroupCells = useCallback((groupId: number) => {
    return manager.getGroupCells(groupId);
  }, [manager]);

  const getGroupsByType = useCallback((type: GroupType) => {
    return manager.getGroupsByType(type);
  }, [manager]);

  const getVisibleGroups = useCallback(() => {
    return manager.getVisibleGroups();
  }, [manager]);

  const getActiveGroups = useCallback(() => {
    return manager.getActiveGroups();
  }, [manager]);

  const isGroupEmpty = useCallback((groupId: number) => {
    return manager.isGroupEmpty(groupId);
  }, [manager]);

  // 颜色管理函数
  const getGroupColor = useCallback((groupId: number) => {
    return manager.getGroupColor(groupId);
  }, [manager]);

  const setGroupColor = useCallback((groupId: number, color: string): boolean => {
    try {
      const result = manager.setGroupColor(groupId, color);
      if (result) {
        refreshConfigs();
      }
      return result;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to set group color');
      return false;
    }
  }, [manager, refreshConfigs]);

  // 工具函数
  const getGroupType = useCallback((groupId: number) => {
    return manager.getGroupType(groupId);
  }, [manager]);

  const resetGroupConfigs = useCallback((): void => {
    try {
      manager.resetGroupConfigs();
      refreshConfigs();
      refreshStats();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to reset group configs');
    }
  }, [manager, refreshConfigs, refreshStats]);

  return {
    // 分组配置
    groupConfigs,
    getGroupConfig,
    setGroupConfig,

    // 分组操作
    addCellsToGroup,
    removeCellsFromGroup,
    clearGroup,
    clearAllGroups,

    // 可见性控制
    toggleGroupVisibility,
    setGroupVisibility,
    showAllCrossGroups,
    hideAllCrossGroups,
    showAllDiagonalGroups,
    hideAllDiagonalGroups,
    showAllGroups,
    hideAllGroups,

    // 分组激活
    activateGroup,
    deactivateGroup,
    deactivateAllGroups,

    // 查询功能
    getGroupCells,
    getGroupsByType,
    getVisibleGroups,
    getActiveGroups,
    isGroupEmpty,

    // 颜色管理
    getGroupColor,
    setGroupColor,

    // 统计信息
    stats,
    refreshStats,

    // 工具函数
    getGroupType,
    resetGroupConfigs,

    // 状态
    isLoading,
    error,
  };
}
