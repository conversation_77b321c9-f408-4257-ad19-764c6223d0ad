/**
 * 分组管理器 - 重构版
 * 🎯 核心价值：基于统一CellData结构的分组控制和管理
 * ⚡ 性能优化：批量操作、索引优化、缓存机制
 * 📊 功能范围：十字组(1-10)、交叉组(11-44)的独立控制
 * 🔄 架构设计：基于CellDataManager的统一数据访问
 */

import type { CellData } from '../types/grid';
import { CellDataManager, type GroupType } from './CellDataManager';

// 分组配置接口
export interface GroupConfig {
  id: number;
  name: string;
  type: GroupType;
  isVisible: boolean;
  isActive: boolean;
  color: string;
  description: string;
}

// 分组操作结果
export interface GroupOperationResult {
  success: boolean;
  affectedCells: number;
  message: string;
}

// 分组统计信息
export interface GroupStats {
  totalGroups: number;
  activeGroups: number;
  crossGroups: number;
  diagonalGroups: number;
  cellsInGroups: number;
  groupDistribution: Record<number, number>;
}

/**
 * 分组管理器类
 */
export class GroupManager {
  private static instance: GroupManager;
  private manager: CellDataManager;
  private groupConfigs: Map<number, GroupConfig> = new Map();
  private groupVisibility: Map<number, boolean> = new Map();

  // 分组范围定义
  private readonly CROSS_GROUPS = { min: 1, max: 10 }; // 十字组
  private readonly DIAGONAL_GROUPS = { min: 11, max: 44 }; // 交叉组

  // 分组颜色配置
  private readonly GROUP_COLORS = {
    cross: [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
    ],
    diagonal: [
      '#FF8A80', '#80CBC4', '#81C784', '#FFB74D', '#F48FB1',
      '#CE93D8', '#90CAF9', '#A5D6A7', '#FFCC02', '#BCAAA4'
    ]
  };

  private constructor() {
    this.manager = CellDataManager.getInstance();
    this.initializeGroupConfigs();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): GroupManager {
    if (!GroupManager.instance) {
      GroupManager.instance = new GroupManager();
    }
    return GroupManager.instance;
  }

  /**
   * 初始化分组配置
   */
  private initializeGroupConfigs(): void {
    // 初始化十字组 (1-10)
    for (let i = this.CROSS_GROUPS.min; i <= this.CROSS_GROUPS.max; i++) {
      const config: GroupConfig = {
        id: i,
        name: `十字组${i}`,
        type: 'cross',
        isVisible: true,
        isActive: false,
        color: this.GROUP_COLORS.cross[(i - 1) % this.GROUP_COLORS.cross.length],
        description: `十字组${i} - 垂直和水平方向的分组`,
      };
      this.groupConfigs.set(i, config);
      this.groupVisibility.set(i, true);
    }

    // 初始化交叉组 (11-44)
    for (let i = this.DIAGONAL_GROUPS.min; i <= this.DIAGONAL_GROUPS.max; i++) {
      const config: GroupConfig = {
        id: i,
        name: `交叉组${i}`,
        type: 'diagonal',
        isVisible: true,
        isActive: false,
        color: this.GROUP_COLORS.diagonal[(i - 11) % this.GROUP_COLORS.diagonal.length],
        description: `交叉组${i} - 对角线和其他交叉模式分组`,
      };
      this.groupConfigs.set(i, config);
      this.groupVisibility.set(i, true);
    }
  }

  /**
   * 获取分组配置
   */
  public getGroupConfig(groupId: number): GroupConfig | undefined {
    return this.groupConfigs.get(groupId);
  }

  /**
   * 设置分组配置
   */
  public setGroupConfig(groupId: number, config: Partial<GroupConfig>): boolean {
    const currentConfig = this.groupConfigs.get(groupId);
    if (!currentConfig) return false;

    const updatedConfig = { ...currentConfig, ...config };
    this.groupConfigs.set(groupId, updatedConfig);
    
    // 更新可见性映射
    if (config.isVisible !== undefined) {
      this.groupVisibility.set(groupId, config.isVisible);
    }

    return true;
  }

  /**
   * 获取分组类型
   */
  public getGroupType(groupId: number): GroupType | null {
    if (groupId >= this.CROSS_GROUPS.min && groupId <= this.CROSS_GROUPS.max) {
      return 'cross';
    }
    if (groupId >= this.DIAGONAL_GROUPS.min && groupId <= this.DIAGONAL_GROUPS.max) {
      return 'diagonal';
    }
    return null;
  }

  /**
   * 获取指定类型的所有分组
   */
  public getGroupsByType(type: GroupType): GroupConfig[] {
    return Array.from(this.groupConfigs.values()).filter(config => config.type === type);
  }

  /**
   * 获取分组中的所有单元格
   */
  public getGroupCells(groupId: number): CellData[] {
    return this.manager.getCellsByGroup(groupId);
  }

  /**
   * 将单元格添加到分组
   */
  public addCellsToGroup(cellIds: string[], groupId: number): GroupOperationResult {
    try {
      const updates = cellIds.map(id => ({
        id,
        data: { group: groupId }
      }));

      const success = this.manager.batchUpdate(updates);
      
      return {
        success,
        affectedCells: success ? cellIds.length : 0,
        message: success ? `成功将${cellIds.length}个单元格添加到分组${groupId}` : '添加失败'
      };
    } catch (error) {
      return {
        success: false,
        affectedCells: 0,
        message: `添加失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }

  /**
   * 从分组中移除单元格
   */
  public removeCellsFromGroup(cellIds: string[]): GroupOperationResult {
    try {
      const updates = cellIds.map(id => ({
        id,
        data: { group: 0 } // 0表示无分组
      }));

      const success = this.manager.batchUpdate(updates);
      
      return {
        success,
        affectedCells: success ? cellIds.length : 0,
        message: success ? `成功从分组中移除${cellIds.length}个单元格` : '移除失败'
      };
    } catch (error) {
      return {
        success: false,
        affectedCells: 0,
        message: `移除失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }

  /**
   * 切换分组可见性
   */
  public toggleGroupVisibility(groupId: number): boolean {
    const currentVisibility = this.groupVisibility.get(groupId) ?? true;
    const newVisibility = !currentVisibility;
    
    this.groupVisibility.set(groupId, newVisibility);
    this.setGroupConfig(groupId, { isVisible: newVisibility });
    
    return newVisibility;
  }

  /**
   * 设置分组可见性
   */
  public setGroupVisibility(groupId: number, isVisible: boolean): void {
    this.groupVisibility.set(groupId, isVisible);
    this.setGroupConfig(groupId, { isVisible });
  }

  /**
   * 激活分组
   */
  public activateGroup(groupId: number): boolean {
    // 先取消其他分组的激活状态
    this.groupConfigs.forEach((config, id) => {
      if (id !== groupId && config.isActive) {
        this.setGroupConfig(id, { isActive: false });
      }
    });

    // 激活指定分组
    return this.setGroupConfig(groupId, { isActive: true });
  }

  /**
   * 取消分组激活
   */
  public deactivateGroup(groupId: number): boolean {
    return this.setGroupConfig(groupId, { isActive: false });
  }

  /**
   * 取消所有分组激活
   */
  public deactivateAllGroups(): void {
    this.groupConfigs.forEach((config, id) => {
      if (config.isActive) {
        this.setGroupConfig(id, { isActive: false });
      }
    });
  }

  /**
   * 显示所有十字组
   */
  public showAllCrossGroups(): void {
    for (let i = this.CROSS_GROUPS.min; i <= this.CROSS_GROUPS.max; i++) {
      this.setGroupVisibility(i, true);
    }
  }

  /**
   * 隐藏所有十字组
   */
  public hideAllCrossGroups(): void {
    for (let i = this.CROSS_GROUPS.min; i <= this.CROSS_GROUPS.max; i++) {
      this.setGroupVisibility(i, false);
    }
  }

  /**
   * 显示所有交叉组
   */
  public showAllDiagonalGroups(): void {
    for (let i = this.DIAGONAL_GROUPS.min; i <= this.DIAGONAL_GROUPS.max; i++) {
      this.setGroupVisibility(i, true);
    }
  }

  /**
   * 隐藏所有交叉组
   */
  public hideAllDiagonalGroups(): void {
    for (let i = this.DIAGONAL_GROUPS.min; i <= this.DIAGONAL_GROUPS.max; i++) {
      this.setGroupVisibility(i, false);
    }
  }

  /**
   * 显示所有分组
   */
  public showAllGroups(): void {
    this.showAllCrossGroups();
    this.showAllDiagonalGroups();
  }

  /**
   * 隐藏所有分组
   */
  public hideAllGroups(): void {
    this.hideAllCrossGroups();
    this.hideAllDiagonalGroups();
  }

  /**
   * 清空指定分组
   */
  public clearGroup(groupId: number): GroupOperationResult {
    const groupCells = this.getGroupCells(groupId);
    const cellIds = groupCells.map(cell => cell.id);
    
    return this.removeCellsFromGroup(cellIds);
  }

  /**
   * 清空所有分组
   */
  public clearAllGroups(): GroupOperationResult {
    try {
      const allCells = this.manager.getAllCells();
      const cellsInGroups = allCells.filter(cell => cell.group > 0);
      const cellIds = cellsInGroups.map(cell => cell.id);
      
      return this.removeCellsFromGroup(cellIds);
    } catch (error) {
      return {
        success: false,
        affectedCells: 0,
        message: `清空失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }

  /**
   * 获取分组统计信息
   */
  public getGroupStats(): GroupStats {
    const allCells = this.manager.getAllCells();
    const cellsInGroups = allCells.filter(cell => cell.group > 0);
    
    // 计算分组分布
    const groupDistribution: Record<number, number> = {};
    cellsInGroups.forEach(cell => {
      groupDistribution[cell.group] = (groupDistribution[cell.group] || 0) + 1;
    });

    // 计算活跃分组数
    const activeGroups = Array.from(this.groupConfigs.values()).filter(config => config.isActive).length;

    return {
      totalGroups: this.groupConfigs.size,
      activeGroups,
      crossGroups: this.CROSS_GROUPS.max - this.CROSS_GROUPS.min + 1,
      diagonalGroups: this.DIAGONAL_GROUPS.max - this.DIAGONAL_GROUPS.min + 1,
      cellsInGroups: cellsInGroups.length,
      groupDistribution,
    };
  }

  /**
   * 获取可见的分组
   */
  public getVisibleGroups(): GroupConfig[] {
    return Array.from(this.groupConfigs.values()).filter(config => config.isVisible);
  }

  /**
   * 获取活跃的分组
   */
  public getActiveGroups(): GroupConfig[] {
    return Array.from(this.groupConfigs.values()).filter(config => config.isActive);
  }

  /**
   * 检查分组是否为空
   */
  public isGroupEmpty(groupId: number): boolean {
    const groupCells = this.getGroupCells(groupId);
    return groupCells.length === 0;
  }

  /**
   * 获取分组颜色
   */
  public getGroupColor(groupId: number): string {
    const config = this.getGroupConfig(groupId);
    return config?.color || '#CCCCCC';
  }

  /**
   * 设置分组颜色
   */
  public setGroupColor(groupId: number, color: string): boolean {
    return this.setGroupConfig(groupId, { color });
  }

  /**
   * 重置分组配置
   */
  public resetGroupConfigs(): void {
    this.groupConfigs.clear();
    this.groupVisibility.clear();
    this.initializeGroupConfigs();
  }
}
