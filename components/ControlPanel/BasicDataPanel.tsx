import React, { memo, useState, useCallback } from 'react';
import type { CellData } from '../../types/grid';
import { useCellDataManager } from '../../hooks/useCellDataManager';
import { useColorRenderer } from '../../hooks/useColorRenderer';
import { type ColorType } from '../../utils/CellDataManager';
import { COLOR_HEX_MAP, LEVEL_AVAILABILITY } from '../../utils/cellDataHelpers';

/**
 * 基础数据面板组件 - 重构版
 * 🎯 核心价值：基于统一CellData结构管理数据
 * ⚡ 性能优化：使用memo包装，基于CellDataManager
 * 📊 功能范围：颜色管理、级别控制、数据统计
 * 🔄 重构状态：已基于CellDataManager重构完成
 */

interface BasicDataPanelProps {
  className?: string;
}

export const BasicDataPanel = memo<BasicDataPanelProps>(({ className = '' }) => {
  const [activeColorTab, setActiveColorTab] = useState<ColorType | 'black'>('red');

  // 使用新的数据管理Hook
  const {
    stats,
    getCellsByColor,
    getCellsByLevel,
    getCellsByColorAndLevel,
    batchUpdateColors,
    error,
    isLoading,
  } = useCellDataManager();

  // 使用颜色渲染Hook
  const {
    getColorConfig,
    setColorConfig,
    toggleColorVisibility,
    toggleLevelVisibility,
    showAllColors,
    hideAllColors,
    showAllLevels,
    hideAllLevels,
  } = useColorRenderer();

  // 处理颜色标签切换
  const handleColorTabChange = useCallback((color: ColorType | 'black') => {
    setActiveColorTab(color);
  }, []);

  // 处理颜色可见性切换
  const handleColorVisibilityToggle = useCallback(() => {
    if (activeColorTab !== 'black') {
      toggleColorVisibility(activeColorTab as ColorType);
    }
  }, [activeColorTab, toggleColorVisibility]);

  // 处理级别可见性切换
  const handleLevelToggle = useCallback((level: 1 | 2 | 3 | 4) => {
    if (activeColorTab !== 'black') {
      toggleLevelVisibility(activeColorTab as ColorType, level);
    }
  }, [activeColorTab, toggleLevelVisibility]);

  // 获取颜色名称
  const getColorName = (color: ColorType | 'black'): string => {
    const names = {
      red: '红色', cyan: '青色', yellow: '黄色', purple: '紫色',
      orange: '橙色', green: '绿色', blue: '蓝色', pink: '粉色', black: '黑色'
    };
    return names[color] || '未知';
  };

  // 渲染颜色标签
  const renderColorTabs = () => {
    const colors: (ColorType | 'black')[] = ['red', 'cyan', 'yellow', 'purple', 'orange', 'green', 'blue', 'pink', 'black'];

    return (
      <div className="flex flex-wrap gap-1 mb-4">
        {colors.map((color) => (
          <button
            key={color}
            onClick={() => handleColorTabChange(color)}
            className={`px-3 py-1 text-xs rounded border transition-colors ${
              activeColorTab === color
                ? 'bg-blue-600 text-white border-blue-500'
                : 'bg-transparent text-gray-600 border-gray-300 hover:bg-gray-100'
            }`}
          >
            {getColorName(color)}
          </button>
        ))}
      </div>
    );
  };

  // 渲染级别控制
  const renderLevelControls = () => {
    if (activeColorTab === 'black') {
      return (
        <div className="p-3 bg-gray-50 rounded border">
          <div className="text-sm text-gray-600">黑色格子没有级别控制</div>
        </div>
      );
    }

    const currentColor = activeColorTab as ColorType;
    const config = getColorConfig(currentColor);
    const availableLevels = LEVEL_AVAILABILITY[currentColor] || [];

    return (
      <div className="space-y-3">
        {/* 总控制 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {getColorName(currentColor)}格子总控制
          </label>
          <button
            onClick={handleColorVisibilityToggle}
            className={`w-full p-2 text-sm rounded border transition-colors ${
              config.showCells
                ? 'bg-green-600 text-white border-green-500'
                : 'bg-gray-200 text-gray-600 border-gray-300 hover:bg-gray-300'
            }`}
          >
            {config.showCells ? `隐藏所有${getColorName(currentColor)}格子` : `显示所有${getColorName(currentColor)}格子`}
          </button>
        </div>

        {/* 级别控制 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {getColorName(currentColor)}级别控制
          </label>
          <div className="grid grid-cols-4 gap-1">
            {[1, 2, 3, 4].map((level) => {
              const isAvailable = availableLevels.includes(level as 1 | 2 | 3 | 4);
              const levelKey = `showLevel${level}` as keyof typeof config;
              const isActive = config[levelKey] as boolean;

              return (
                <button
                  key={level}
                  onClick={() => handleLevelToggle(level as 1 | 2 | 3 | 4)}
                  disabled={!isAvailable}
                  className={`p-1.5 text-xs rounded border transition-colors ${
                    isActive && isAvailable
                      ? 'bg-blue-600 text-white border-blue-500'
                      : 'bg-gray-100 text-gray-600 border-gray-300 hover:bg-gray-200'
                  } ${!isAvailable ? 'opacity-30 cursor-not-allowed' : ''}`}
                >
                  {level}级{!isAvailable && ' (无)'}
                </button>
              );
            })}
          </div>
        </div>
      </div>
    );
  };

  // 渲染统计信息
  const renderStats = () => (
    <div className="space-y-2">
      <h3 className="text-sm font-medium text-gray-700">数据统计</h3>
      <div className="grid grid-cols-2 gap-2 text-xs">
        <div className="p-2 bg-gray-50 rounded">
          <div className="text-gray-500">总单元格</div>
          <div className="font-medium">{stats.totalCells}</div>
        </div>
        <div className="p-2 bg-gray-50 rounded">
          <div className="text-gray-500">分组数量</div>
          <div className="font-medium">{stats.groupCount}</div>
        </div>
      </div>
      {activeColorTab !== 'black' && (
        <div className="p-2 bg-blue-50 rounded">
          <div className="text-blue-600 text-xs">
            {getColorName(activeColorTab as ColorType)}格子: {getCellsByColor(COLOR_HEX_MAP[activeColorTab as ColorType]).length}个
          </div>
        </div>
      )}
    </div>
  );

  if (isLoading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="p-4 bg-gray-50 rounded border">
          <div className="text-sm text-gray-600">加载中...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="p-4 bg-red-50 rounded border border-red-200">
          <div className="text-sm font-medium text-red-800">错误</div>
          <div className="text-xs text-red-600">{error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 颜色选项卡 */}
      {renderColorTabs()}

      {/* 级别控制 */}
      {renderLevelControls()}

      {/* 统计信息 */}
      {renderStats()}

      {/* 批量操作 */}
      <div className="space-y-2">
        <h3 className="text-sm font-medium text-gray-700">批量操作</h3>
        <div className="grid grid-cols-2 gap-2">
          <button
            onClick={showAllColors}
            className="p-2 text-xs bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
          >
            显示所有颜色
          </button>
          <button
            onClick={hideAllColors}
            className="p-2 text-xs bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
          >
            隐藏所有颜色
          </button>
        </div>
      </div>
    </div>
  );
});

BasicDataPanel.displayName = 'BasicDataPanel';
